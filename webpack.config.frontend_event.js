const path = require('node:path');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const glob = require('glob');

const appFiles = glob.sync([
    './frontend_event/sport-wrench.module.js',
    './frontend_event/**/*.js',
    '!./frontend_event/vendor.js', // Exclude vendor file from app bundle
], { absolute: true });

const entryFiles = {
    vendor: './frontend_event/vendor.js',
    main: [
        ...appFiles,
        './assets/stylesEsw/main.scss',
    ],
};

module.exports = {
    extends: path.resolve(__dirname, './webpack.config.base.js'),

    entry: entryFiles,

    plugins: [
        {
            apply: (compiler) => {
                new CopyWebpackPlugin({
                    patterns: [
                        { context: './frontend_event/', from: '**/*.html', to: '[path][name][ext]' },
                    ],
                }).apply(compiler);
            }
        }
    ],

    devServer: {
        port: 8078,
    },
};
