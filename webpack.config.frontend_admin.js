const path = require('node:path');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const glob = require('glob');

const appFiles = glob.sync([
    './frontend_admin/sport-wrench-admin.module.js',
    './frontend_admin/**/*.js',
    '!./frontend_admin/vendor.js', // Exclude vendor file from app bundle
], { absolute: true });

const entryFiles = {
    vendor: './frontend_admin/vendor.js',
    main: [
        ...appFiles,
        './assets/styles/main.scss',
        './assets/styles/admin.scss',
    ],
};

module.exports = {
    extends: path.resolve(__dirname, './webpack.config.base.js'),

    entry: entryFiles,

    plugins: [
        {
            apply: (compiler) => {
                new CopyWebpackPlugin({
                    patterns: [
                        { context: './frontend_admin/', from: '**/*.html', to: '[path][name][ext]' },
                    ],
                }).apply(compiler);
            }
        }
    ],

    devServer: {
        port: 8087,
    },
};
