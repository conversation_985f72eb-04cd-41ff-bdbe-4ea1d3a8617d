// Frontend Event Vendor Dependencies
// This file imports all vendor dependencies for the event frontend

// Core Libraries
import 'jquery';
import 'underscore';
import 'angular';
import 'angular-sanitize';

// UI Router (modern version)
import '@uirouter/angularjs';

// Storage
import 'ngstorage';

// UI Bootstrap (standard version for events)
import 'angular-ui-bootstrap';

// Additional event dependencies
import 'angular-resource';
import 'angular-cookies';

// jQuery UI (custom - keep from assets)
import '../assets/js/jquery-ui/jquery-ui.min.js';

// Date/Time
import 'moment';
import 'angular-ui-mask';

// Lazy Loading
import 'oclazyload';

// Loading Bar
import 'angular-loading-bar';
import 'angular-loading-bar/build/loading-bar.css';

// Tables
import 'ng-table';
import 'ng-table/dist/ng-table.css';

// Clipboard
import 'angular-clipboard';

// Infinite Scroll
import 'ng-infinite-scroll';

// DateTime Picker
import 'bootstrap-datepicker';

// Animations
import 'angular-animate';

// Toastr
import 'angular-toastr';
import 'angular-toastr/dist/angular-toastr.css';

// UI Select
import 'ui-select';
import 'ui-select/dist/select.css';

// Color Picker
import 'angular-bootstrap-colorpicker';

// Dropdown Multiselect
import 'angularjs-dropdown-multiselect';

// Drag and Drop
import 'angular-dragdrop';

// Selectable
// Note: ngSelectable might need to be handled as a custom file
// import '../assets/js/ngSelectable/ngSelectable.js';

// Validation (custom - keep from assets)
import '../assets/js/validation/angular-validation.js';
import '../assets/js/validation/angular-validation-rule.js';

// Breadcrumbs
import 'angular-utils-ui-breadcrumbs';

// Google Maps
import 'angular-simple-logger';
import 'angular-google-maps';

// Font Awesome
import 'font-awesome/css/font-awesome.css';

// Angular CSP
import 'angular/angular-csp.css';
