// Frontend Vendor Dependencies
// This file imports all vendor dependencies for the main frontend

// Polyfills (keep these from assets/js)
import '../assets/js/polyfills/babel-polyfill.js';
import '../assets/js/polyfills/array-includes.js';
import '../assets/js/polyfills/string-includes.js';

// Core Libraries
import 'jquery';
import 'underscore';
import 'angular';
import 'angular-sanitize';

// UI Router (modern version)
import '@uirouter/angularjs';

// Storage
import 'ngstorage';

// UI Bootstrap (custom version - keep from assets)
import '../assets/js/uib-custom/ui-bootstrap-custom-tpls-1.3.3.min.js';

// Date/Time
import 'moment';
import 'angular-ui-mask';

// Lazy Loading
import 'oclazyload';

// Loading Bar
import 'angular-loading-bar';
import 'angular-loading-bar/build/loading-bar.css';

// Table Headers
import 'stickytableheaders';

// Tables
import 'ng-table';
import 'ng-table/dist/ng-table.css';

// Clipboard
import 'angular-clipboard';

// Infinite Scroll
import 'ng-infinite-scroll';

// DateTime Picker
import 'bootstrap-datepicker';
import 'bootstrap-datepicker/dist/css/bootstrap-datepicker3.css';

// Animations
import 'angular-animate';

// Toastr
import 'angular-toastr';
import 'angular-toastr/dist/angular-toastr.css';

// UI Select
import 'ui-select';
import 'ui-select/dist/select.css';

// Color Picker
import 'angular-bootstrap-colorpicker';
import 'angular-bootstrap-colorpicker/css/colorpicker.css';

// Dropdown Multiselect
import 'angularjs-dropdown-multiselect';

// Signature Pad (custom - keep from assets)
import '../assets/js/signature_pad/signature_pad.js';

// Excel/Spreadsheet Libraries
import 'xlsx';

// Google Maps
import 'angular-simple-logger';
import 'angular-google-maps';

// Font Awesome
import 'font-awesome/css/font-awesome.css';

// Angular CSP
import 'angular/angular-csp.css';
