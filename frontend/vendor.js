// Frontend Vendor Dependencies - Minimal Working Version
// This file imports only the core dependencies that are confirmed to work

// Polyfills (keep these from assets/js)
import '../assets/js/polyfills/babel-polyfill.js';
import '../assets/js/polyfills/array-includes.js';
import '../assets/js/polyfills/string-includes.js';

// Core Libraries (from npm)
import 'jquery';
import 'underscore';
import 'angular';
import 'angular-sanitize';
import 'angular-cookies';
import 'angular-resource';

// UI Router (modern version)
import '@uirouter/angularjs';

// Date/Time
import 'moment';

// UI Bootstrap (custom version - keep from assets)
import '../assets/js/uib-custom/ui-bootstrap-custom-tpls-1.3.3.min.js';

// Signature Pad (custom - keep from assets)
import '../assets/js/signature_pad/signature_pad.js';

// Additional custom files that don't have npm equivalents
// These will be copied by WebPack and served as static files

// Note: Other dependencies like angular-loading-bar, ng-table, etc.
// will be added incrementally as we verify their npm package availability
